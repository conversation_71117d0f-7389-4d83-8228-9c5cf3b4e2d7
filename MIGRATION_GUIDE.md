# Migration Guide: Upgrading to Optimized Version

This guide helps you migrate from the original version to the optimized version of the Sangguniang Bayan Ordinance System.

## 🚨 Important: Backup First!

Before starting the migration, **always backup your database and files**:

```bash
# Backup database (SQLite)
cp db.sqlite3 db.sqlite3.backup

# Backup media files
cp -r media media_backup

# Backup entire project
tar -czf sbo_system_backup_$(date +%Y%m%d).tar.gz .
```

## 📋 Migration Steps

### Step 1: Update Dependencies

1. **Install new dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install development dependencies** (optional):
   ```bash
   pip install black flake8 isort mypy pytest pytest-django pytest-cov
   ```

### Step 2: Environment Configuration

1. **Create environment file**:
   ```bash
   cp .env.example .env
   ```

2. **Configure your environment variables** in `.env`:
   ```bash
   SECRET_KEY=your-existing-secret-key
   DEBUG=True  # Set to False for production
   ALLOWED_HOSTS=localhost,127.0.0.1
   
   # Database (keep existing SQLite for now)
   DB_ENGINE=django.db.backends.sqlite3
   DB_NAME=db.sqlite3
   ```

### Step 3: Database Migration

1. **Create new migrations**:
   ```bash
   python manage.py makemigrations ordinances
   ```

2. **Apply migrations**:
   ```bash
   python manage.py migrate
   ```

3. **Verify data integrity**:
   ```bash
   python manage.py shell
   ```
   ```python
   from ordinances.models import Ordinance, Category, Sponsor
   
   # Check counts
   print(f"Ordinances: {Ordinance.objects.count()}")
   print(f"Categories: {Category.objects.count()}")
   print(f"Sponsors: {Sponsor.objects.count()}")
   
   # Test new methods
   ordinance = Ordinance.objects.first()
   if ordinance:
       print(f"Is public: {ordinance.is_public()}")
       print(f"Status color: {ordinance.get_status_color()}")
   ```

### Step 4: Update Custom Code (if any)

If you have custom modifications, update them to use the new service layer:

**Before (in views.py)**:
```python
ordinances = Ordinance.objects.filter(status__in=['approved', 'published'])
```

**After (using services)**:
```python
from ordinances.services import OrdinanceService
ordinances = OrdinanceService.get_public_ordinances()
```

### Step 5: Test the Application

1. **Run tests**:
   ```bash
   python -m pytest ordinances/tests/
   ```

2. **Start development server**:
   ```bash
   python manage.py runserver
   ```

3. **Test key functionality**:
   - Browse ordinances
   - Search functionality
   - Admin dashboard
   - Create/edit ordinances
   - File uploads

### Step 6: Performance Optimization

1. **Optimize database**:
   ```bash
   python manage.py optimize_database --analyze
   ```

2. **Clear cache**:
   ```bash
   python manage.py shell
   ```
   ```python
   from django.core.cache import cache
   cache.clear()
   ```

## 🔄 Rollback Plan

If you encounter issues, you can rollback:

1. **Restore database**:
   ```bash
   cp db.sqlite3.backup db.sqlite3
   ```

2. **Restore media files**:
   ```bash
   rm -rf media
   cp -r media_backup media
   ```

3. **Checkout previous version**:
   ```bash
   git checkout previous-version-tag
   ```

## 🐳 Docker Migration

If you want to migrate to Docker:

1. **Build Docker image**:
   ```bash
   docker-compose build
   ```

2. **Migrate data to Docker**:
   ```bash
   # Copy database to Docker volume
   docker-compose up -d db
   docker cp db.sqlite3 sbo_system_db_1:/var/lib/postgresql/data/
   
   # Or use PostgreSQL dump/restore for better compatibility
   ```

3. **Start services**:
   ```bash
   docker-compose up -d
   ```

## 🔍 Verification Checklist

After migration, verify these features work:

- [ ] Home page loads correctly
- [ ] Ordinance list with pagination
- [ ] Search functionality
- [ ] Ordinance detail pages
- [ ] PDF export
- [ ] Admin login
- [ ] Admin dashboard
- [ ] Create new ordinance
- [ ] Edit existing ordinance
- [ ] File uploads
- [ ] Category management
- [ ] Sponsor management
- [ ] Official management

## 🚨 Common Issues & Solutions

### Issue: Migration fails with validation errors

**Solution**: Some existing data might not meet new validation requirements.

```bash
# Check for invalid data
python manage.py shell
```
```python
from ordinances.models import Ordinance

# Find ordinances with short titles
short_titles = Ordinance.objects.filter(title__length__lt=10)
print(f"Ordinances with short titles: {short_titles.count()}")

# Fix them manually or update validation
for ord in short_titles:
    if len(ord.title) < 10:
        ord.title = f"{ord.title} - Municipal Ordinance"
        ord.save()
```

### Issue: Cache errors

**Solution**: Clear all cache and restart:

```bash
python manage.py shell -c "from django.core.cache import cache; cache.clear()"
```

### Issue: Static files not loading

**Solution**: Collect static files:

```bash
python manage.py collectstatic --noinput
```

### Issue: Permission errors

**Solution**: Check file permissions:

```bash
chmod -R 755 media/
chmod -R 755 static/
```

## 📞 Support

If you encounter issues during migration:

1. Check the logs in `logs/django.log`
2. Run with DEBUG=True to see detailed error messages
3. Check the GitHub issues for similar problems
4. Create a new issue with:
   - Error message
   - Steps to reproduce
   - Your environment details

## 🎯 Post-Migration Optimization

After successful migration:

1. **Set up monitoring**:
   - Configure logging
   - Set up error tracking (Sentry)
   - Monitor performance

2. **Security hardening**:
   - Change default passwords
   - Configure SSL certificates
   - Set up rate limiting

3. **Performance tuning**:
   - Configure Redis caching
   - Optimize database queries
   - Set up CDN for static files

4. **Backup strategy**:
   - Set up automated backups
   - Test restore procedures
   - Document backup locations
