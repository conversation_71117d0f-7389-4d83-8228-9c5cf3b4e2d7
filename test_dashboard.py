#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sbo_system.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth import get_user_model
from ordinances.views import admin_dashboard

User = get_user_model()

def test_dashboard():
    # Create a test user
    user = User.objects.filter(is_superuser=True).first()
    if not user:
        print("No superuser found. Please create one first.")
        return
    
    # Create a request
    factory = RequestFactory()
    request = factory.get('/admin-dashboard/')
    request.user = user
    
    try:
        # Test the view
        response = admin_dashboard(request)
        print(f"Dashboard view status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Dashboard view works correctly!")
        else:
            print(f"❌ Dashboard view returned status {response.status_code}")
    except Exception as e:
        print(f"❌ Error in dashboard view: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_dashboard()
